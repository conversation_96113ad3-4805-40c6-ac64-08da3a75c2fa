import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/transaction_isar.dart';
import '../models/category_isar.dart';
import '../models/transaction_analytics.dart';
import '../services/transaction_service.dart';
import '../services/transaction_analytics_service.dart';
import 'transaction_controller.dart';
import '../../widgets/filters/filters.dart';

/// Centralized controller for individual transaction detail screens.
/// Manages all state, filtering, sorting, and business logic for both
/// Records and Analytics tabs, ensuring perfect synchronization.
class TransactionDetailController extends ChangeNotifier {
  final String? categoryFilter;
  final String? typeFilter;
  final TransactionService _transactionService;

  // Core data
  List<TransactionIsar> _allRecords = [];
  List<TransactionIsar> _filteredRecords = [];
  List<CategoryIsar> _categories = [];
  bool _isLoading = false;
  String? _error;



  // Analytics cache
  TransactionAnalyticsResult? _analyticsSummary;

  TransactionDetailController({
    required this.categoryFilter,
    required this.typeFilter,
    required TransactionService transactionService,
  }) : _transactionService = transactionService;

  // Getters
  List<TransactionIsar> get allRecords => List.unmodifiable(_allRecords);
  List<TransactionIsar> get filteredRecords => List.unmodifiable(_filteredRecords);
  List<CategoryIsar> get categories => List.unmodifiable(_categories);
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasData => _allRecords.isNotEmpty;
  bool get hasFilteredData => _filteredRecords.isNotEmpty;



  // Analytics getter
  TransactionAnalyticsResult get analyticsSummary {
    if (_analyticsSummary == null) {
      _calculateAnalytics();
    }
    return _analyticsSummary!;
  }

  /// Initialize the controller by loading data
  Future<void> initialize() async {
    await loadData();
  }

  /// Load transaction records based on filters
  Future<void> loadData() async {
    try {
      _setLoading(true);
      _error = null;

      // Load all transactions and categories
      final allTransactions = await _transactionService.getTransactions();
      // TODO: Implement category fetching once available in TransactionService
      // final categories = await _transactionService.getAllCategories();
      final categories = <CategoryIsar>[];

      // Apply base filters (category and type)
      List<TransactionIsar> filteredTransactions = allTransactions;

      if (categoryFilter != null && categoryFilter!.isNotEmpty) {
        filteredTransactions = filteredTransactions
            .where((t) => t.category == categoryFilter)
            .toList();
      }

      if (typeFilter != null && typeFilter!.isNotEmpty) {
        filteredTransactions = filteredTransactions
            .where((t) => t.categoryType == typeFilter)
            .toList();
      }

      _allRecords = filteredTransactions;
      _categories = categories;
      _filteredRecords = List.from(_allRecords);
      _calculateAnalytics();
      
      _setLoading(false);
    } catch (e) {
      _error = 'Error loading transaction records: $e';
      _setLoading(false);
      rethrow;
    }
  }

  /// Refresh data from the database
  Future<void> refresh() async {
    await loadData();
  }

  /// Apply filters using the universal filter service
  void applyFilters(FilterController filterController) {
    List<TransactionIsar> result = List.from(_allRecords);

    // Apply search filter
    result = FilterHandlers.applySearch<TransactionIsar>(
      items: result,
      searchQuery: filterController.searchQuery,
      searchFields: [
        (transaction) => transaction.description,
        (transaction) => transaction.category,
      ],
    );

    // Apply date range filter
    result = FilterHandlers.applyDateFilter<TransactionIsar>(
      items: result,
      startDate: filterController.startDate,
      endDate: filterController.endDate,
      dateExtractor: (transaction) => transaction.date,
    );

    // Apply sorting
    result = FilterHandlers.applySort<TransactionIsar>(
      items: result,
      sortField: filterController.sortBy,
      isAscending: filterController.isAscending,
      sortComparators: {
        'date': FilterHandlers.dateComparator<TransactionIsar>((transaction) => transaction.date),
        'amount': FilterHandlers.numericComparator<TransactionIsar>((transaction) => transaction.amount),
        'description': FilterHandlers.stringComparator<TransactionIsar>((transaction) => transaction.description),
        'category': FilterHandlers.stringComparator<TransactionIsar>((transaction) => transaction.category),
      },
    );

    _filteredRecords = result;

    _calculateAnalytics();
    notifyListeners();
  }

  /// Legacy method for backward compatibility - now handled by applyFilters
  @Deprecated('Use applyFilters(FilterController) instead')
  void setDateRange(DateTime? startDate, DateTime? endDate) {
    // This method is deprecated but kept for backward compatibility
    // The filtering is now handled by the universal filter system
  }

  /// Legacy method for backward compatibility - now handled by applyFilters
  @Deprecated('Use applyFilters(FilterController) instead')
  void clearFilters() {
    // This method is deprecated but kept for backward compatibility
    // The filtering is now handled by the universal filter system
  }

  /// Legacy method for backward compatibility - now handled by applyFilters
  @Deprecated('Use applyFilters(FilterController) instead')
  void setSorting(String sortBy, bool ascending) {
    // This method is deprecated but kept for backward compatibility
    // The sorting is now handled by the universal filter system
  }

  /// Legacy getters for backward compatibility
  @Deprecated('Filter state is now managed by FilterController')
  DateTime? get startDate => null;

  @Deprecated('Filter state is now managed by FilterController')
  DateTime? get endDate => null;

  @Deprecated('Sort state is now managed by FilterController')
  String? get sortBy => null;

  @Deprecated('Sort state is now managed by FilterController')
  bool get sortAscending => false;











  /// Calculate analytics for the filtered records
  void _calculateAnalytics() {
    if (_filteredRecords.isEmpty) {
      _analyticsSummary = TransactionAnalyticsResult.empty;
      return;
    }

    double totalIncome = 0;
    double totalExpenses = 0;
    int incomeCount = 0;
    int expenseCount = 0;
    Map<String, double> categoryBreakdown = {};
    Map<String, double> paymentMethodBreakdown = {};
    DateTime? firstDate;
    DateTime? lastDate;

    for (final transaction in _filteredRecords) {
      final amount = transaction.amount;
      final isIncome = transaction.categoryType.toLowerCase() == 'income';

      if (isIncome) {
        totalIncome += amount;
        incomeCount++;
      } else {
        totalExpenses += amount;
        expenseCount++;
      }

      // Category breakdown
      categoryBreakdown[transaction.category] = 
        (categoryBreakdown[transaction.category] ?? 0) + amount;

      // Payment method breakdown
      paymentMethodBreakdown[transaction.paymentMethod] = 
        (paymentMethodBreakdown[transaction.paymentMethod] ?? 0) + amount;

      // Date tracking
      if (firstDate == null || transaction.date.isBefore(firstDate)) {
        firstDate = transaction.date;
      }
      if (lastDate == null || transaction.date.isAfter(lastDate)) {
        lastDate = transaction.date;
      }
    }

    final netBalance = totalIncome - totalExpenses;
    final averageIncome = incomeCount > 0 ? (totalIncome / incomeCount).toDouble() : 0.0;
    final averageExpense = expenseCount > 0 ? (totalExpenses / expenseCount).toDouble() : 0.0;

    // Use the proper analytics service to calculate results
    _analyticsSummary = TransactionAnalyticsService.calculate(_filteredRecords, _categories);
  }

  /// Private helper methods
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

}
